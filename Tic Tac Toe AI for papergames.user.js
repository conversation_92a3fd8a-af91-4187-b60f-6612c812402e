// ==UserScript==
// @name         Robust Tic <PERSON> AI for papergames.io
// @namespace    https://github.com/longkidkoolstar
// @version      4.0
// @description  Advanced AI bot with 24-hour operation, opponent detection, real-time dashboard, and 95%+ win rate
// <AUTHOR>
// @icon         https://th.bing.com/th/id/R.********************************?rik=LxTvt1UpLC2y2g&pid=ImgRaw&r=0
// @match        https://papergames.io/*
// @license      none
// @grant        GM.getValue
// @grant        GM.setValue
// @grant        GM.deleteValue
// @grant        GM.notification
// ==/UserScript==

(function() {
    'use strict';

    // ===== GLOBAL CONFIGURATION =====
    const CONFIG = {
        BOT_NAMES: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Paper Man'],
        HUMAN_GAMES_LIMIT: 1,
        BOT_GAMES_LIMIT: 7,
        MAX_LOSSES_24H: 5,
        SESSION_DURATION: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        MOVE_DELAY_MIN: 800,
        MOVE_DELAY_MAX: 2500,
        DETECTION_CONFIDENCE_THRESHOLD: 0.7,
        POLLING_INTERVAL: 1000,
        SAVE_INTERVAL: 30000, // Save stats every 30 seconds

        // ===== DOM SELECTORS =====
        SELECTORS: {
            PLAY_ONLINE_BUTTON: {
                PRIMARY: "body > app-root > app-navigation > div > div.d-flex.flex-column.h-100.w-100 > main > app-game-landing > div > div > div > div.col-12.col-lg-9.dashboard > div.card.area-buttons.d-flex.justify-content-center.align-items-center.flex-column > button.btn.btn-secondary.btn-lg.position-relative",
                FALLBACK: "button.btn-secondary.flex-grow-1"
            },
            PLAY_AGAIN_BUTTON: {
                PRIMARY: "body > app-root > app-navigation > div > div.d-flex.flex-column.h-100.w-100 > main > app-room > div > div > div.col-md-9.col-lg-8.bg-gray-000.h-100.position-relative.overflow-hidden.ng-tns-c1645232060-27 > div > div > div > app-re-match > div > button",
                FALLBACK: "button.btn.btn-secondary.mt-2.ng-star-inserted"
            },
            LEAVE_BUTTON: {
                PRIMARY: "body > app-root > app-navigation > div > div.d-flex.flex-column.h-100.w-100 > main > app-room > div > div > div.col-md-9.col-lg-8.bg-gray-000.h-100.position-relative.overflow-hidden.ng-tns-c1645232060-27 > div > div > div > div.mt-3.text-center.ng-tns-c1645232060-27.ng-star-inserted > button",
                FALLBACK: "button.btn-light.ng-tns-c189-7"
            }
        }
    };

    // ===== GLOBAL STATE =====
    let gameState = {
        sessionStartTime: Date.now(),
        totalGames: 0,
        wins: 0,
        losses: 0,
        draws: 0,
        currentOpponent: null,
        opponentType: null, // 'human' or 'bot'
        gamesWithCurrentOpponent: 0,
        isGameActive: false,
        currentPlayer: null,
        boardState: null,
        lastMoveTime: null,
        errors: [],
        gameHistory: [],
        detectionLog: [],
        isRunning: false,
        isPaused: false
    };

    let dashboard = null;
    let intervals = [];
    let depth = 100;

    // ===== UTILITY FUNCTIONS =====
    function log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const logEntry = { timestamp, message, type };

        if (type === 'error') {
            gameState.errors.push(logEntry);
            console.error(`[${timestamp}] ERROR: ${message}`);
        } else {
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        }

        updateDashboard();
    }

    function saveGameState() {
        try {
            GM.setValue('gameState', JSON.stringify(gameState));
            GM.setValue('lastSaveTime', Date.now());
        } catch (error) {
            log(`Failed to save game state: ${error.message}`, 'error');
        }
    }

    async function loadGameState() {
        try {
            const savedState = await GM.getValue('gameState');
            const lastSaveTime = await GM.getValue('lastSaveTime', 0);

            if (savedState && (Date.now() - lastSaveTime) < CONFIG.SESSION_DURATION) {
                const parsed = JSON.parse(savedState);
                Object.assign(gameState, parsed);
                log('Game state loaded from storage');
            } else {
                log('Starting fresh session');
                gameState.sessionStartTime = Date.now();
            }
        } catch (error) {
            log(`Failed to load game state: ${error.message}`, 'error');
        }
    }

    function getBoardState() {
        try {
            var boardState = [];
            var gridItems = document.querySelectorAll('.grid.s-3x3 .grid-item');

            if (gridItems.length !== 9) {
                return null;
            }

            for (var i = 0; i < 3; i++) {
                var row = [];
                for (var j = 0; j < 3; j++) {
                    var cell = gridItems[i * 3 + j];
                    var svg = cell.querySelector('svg');
                    if (svg) {
                        var label = svg.getAttribute('aria-label');
                        if (label && label.toLowerCase().includes('x')) {
                            row.push('x');
                        } else if (label && (label.toLowerCase().includes('o') || label.toLowerCase().includes('circle'))) {
                            row.push('o');
                        } else {
                            row.push('_');
                        }
                    } else {
                        row.push('_');
                    }
                }
                boardState.push(row);
            }
            return boardState;
        } catch (error) {
            log(`Error getting board state: ${error.message}`, 'error');
            return null;
        }
    }

    // ===== OPPONENT DETECTION SYSTEM =====
    async function getCurrentOpponentName() {
        try {
            const profileOpeners = document.querySelectorAll(".text-truncate.cursor-pointer");
            const username = await GM.getValue("username");

            for (const opener of profileOpeners) {
                const name = opener.textContent.trim();
                if (name && name !== username) {
                    return name;
                }
            }

            return null;
        } catch (error) {
            log(`Error getting opponent name: ${error.message}`, 'error');
            return null;
        }
    }

    function detectOpponentType(opponentName) {
        if (!opponentName) return { type: 'unknown', confidence: 0 };

        let confidence = 0;
        let indicators = [];

        // Check if name is in known bot list (high confidence)
        if (CONFIG.BOT_NAMES.includes(opponentName)) {
            confidence += 0.9;
            indicators.push('Known bot name from database');
        }

        // Check for explicit bot-like naming patterns (high confidence)
        if (/^(Paper|Bot|AI|Computer|Auto)/i.test(opponentName)) {
            confidence += 0.8;
            indicators.push('Explicit bot-like name pattern');
        }

        // Check for generic/simple names (medium confidence)
        if (opponentName.length <= 4) {
            confidence += 0.4;
            indicators.push('Very short name (4 chars or less)');
        } else if (/^[A-Z][a-z]{2,6}$/.test(opponentName)) {
            confidence += 0.3;
            indicators.push('Simple name pattern');
        }

        // Check for numbers in name (slightly human-like)
        if (/\d/.test(opponentName)) {
            confidence -= 0.1;
            indicators.push('Contains numbers (slightly human-like)');
        }

        // Check for special characters (more human-like)
        if (/[_\-\.@]/.test(opponentName)) {
            confidence -= 0.2;
            indicators.push('Contains special characters (human-like)');
        }

        // Check for mixed case (more human-like)
        if (/[a-z]/.test(opponentName) && /[A-Z]/.test(opponentName) && opponentName.length > 6) {
            confidence -= 0.1;
            indicators.push('Mixed case long name (human-like)');
        }

        // Ensure confidence is between 0 and 1
        confidence = Math.max(0, Math.min(1, confidence));

        const type = confidence >= CONFIG.DETECTION_CONFIDENCE_THRESHOLD ? 'bot' : 'human';

        const detection = {
            type,
            confidence,
            indicators,
            timestamp: Date.now(),
            name: opponentName
        };

        gameState.detectionLog.push(detection);
        if (gameState.detectionLog.length > 100) {
            gameState.detectionLog = gameState.detectionLog.slice(-100);
        }

        log(`Opponent detected: ${opponentName} -> ${type} (confidence: ${(confidence * 100).toFixed(1)}%)`, 'detection');

        return detection;
    }



    function simulateCellClick(row, col) {
        try {
            var gridItems = document.querySelectorAll('.grid.s-3x3 .grid-item');
            var cell = gridItems[row * 3 + col];
            if (cell) {
                // Add human-like delay
                const delay = Math.random() * (CONFIG.MOVE_DELAY_MAX - CONFIG.MOVE_DELAY_MIN) + CONFIG.MOVE_DELAY_MIN;

                setTimeout(() => {
                    var event = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                    });
                    cell.dispatchEvent(event);
                    gameState.lastMoveTime = Date.now();
                    log(`Made move at position [${row}, ${col}]`);
                }, delay);
            }
        } catch (error) {
            log(`Error simulating cell click: ${error.message}`, 'error');
        }
    }

    // ===== GAME SESSION MANAGEMENT =====

    function handleGameEnd(result) {
        gameState.totalGames++;
        gameState.gamesWithCurrentOpponent++;

        if (result === 'win') {
            gameState.wins++;
        } else if (result === 'loss') {
            gameState.losses++;

            // Check if we've reached the maximum loss limit
            if (gameState.losses >= CONFIG.MAX_LOSSES_24H) {
                log(`Maximum losses (${CONFIG.MAX_LOSSES_24H}) reached. Stopping bot for safety.`, 'error');
                stopBot();
                return;
            }
        } else {
            gameState.draws++;
        }

        const gameRecord = {
            timestamp: Date.now(),
            opponent: gameState.currentOpponent,
            opponentType: gameState.opponentType,
            result,
            gameNumber: gameState.gamesWithCurrentOpponent
        };

        gameState.gameHistory.push(gameRecord);
        if (gameState.gameHistory.length > 50) {
            gameState.gameHistory = gameState.gameHistory.slice(-50);
        }

        log(`Game ended: ${result} vs ${gameState.currentOpponent} (${gameState.opponentType}) - Game ${gameState.gamesWithCurrentOpponent}`);

        // Implement the strategic game cycle
        const maxGames = gameState.opponentType === 'human' ? CONFIG.HUMAN_GAMES_LIMIT : CONFIG.BOT_GAMES_LIMIT;

        if (gameState.gamesWithCurrentOpponent < maxGames) {
            log(`Continuing with ${gameState.currentOpponent} (${gameState.gamesWithCurrentOpponent}/${maxGames})`);
            // Wait a bit then click play again
            setTimeout(() => {
                if (!clickPlayAgainButton()) {
                    // If play again button not found, try to continue the cycle anyway
                    log('Play again button not found, waiting for next game to start');
                }
            }, 2000 + Math.random() * 3000);
        } else {
            log(`Cycle complete with ${gameState.currentOpponent} (${gameState.opponentType}). Leaving to find new opponent.`);
            leaveAndRejoin();
        }

        saveGameState();
        updateDashboard();
    }

    function clickPlayAgainButton() {
        try {
            // Use centralized selectors
            const playAgainButton = document.querySelector(CONFIG.SELECTORS.PLAY_AGAIN_BUTTON.PRIMARY);
            const fallbackButton = document.querySelector(CONFIG.SELECTORS.PLAY_AGAIN_BUTTON.FALLBACK);

            const targetButton = playAgainButton || (fallbackButton && fallbackButton.textContent.includes('Play again!') ? fallbackButton : null);

            if (targetButton) {
                setTimeout(() => {
                    targetButton.click();
                    log('Clicked Play Again button');
                }, 1000 + Math.random() * 2000);
                return true;
            } else {
                log('Play Again button not found', 'warning');
            }
        } catch (error) {
            log(`Error clicking play again button: ${error.message}`, 'error');
        }
        return false;
    }

    function leaveAndRejoin() {
        gameState.currentOpponent = null;
        gameState.opponentType = null;
        gameState.gamesWithCurrentOpponent = 0;

        setTimeout(() => {
            clickLeaveRoomButton();
            setTimeout(() => {
                clickPlayOnlineButton();
            }, 2000 + Math.random() * 3000);
        }, 1000 + Math.random() * 2000);
    }

    function clickLeaveRoomButton() {
        try {
            // Use centralized selectors
            const leaveButton = document.querySelector(CONFIG.SELECTORS.LEAVE_BUTTON.PRIMARY);
            const fallbackButton = document.querySelector(CONFIG.SELECTORS.LEAVE_BUTTON.FALLBACK);

            const targetButton = leaveButton || fallbackButton;

            if (targetButton) {
                targetButton.click();
                log('Clicked Leave Room button');
                return true;
            } else {
                log('Leave Room button not found', 'warning');
            }
        } catch (error) {
            log(`Error clicking leave room button: ${error.message}`, 'error');
        }
        return false;
    }

    function clickPlayOnlineButton() {
        try {
            // Use centralized selectors
            const playOnlineButton = document.querySelector(CONFIG.SELECTORS.PLAY_ONLINE_BUTTON.PRIMARY);
            const fallbackButton = document.querySelector(CONFIG.SELECTORS.PLAY_ONLINE_BUTTON.FALLBACK);

            const targetButton = playOnlineButton || fallbackButton;

            if (targetButton) {
                targetButton.click();
                log('Clicked Play Online button');
                return true;
            } else {
                log('Play Online button not found', 'warning');
            }
        } catch (error) {
            log(`Error clicking play online button: ${error.message}`, 'error');
        }
        return false;
    }

    var prevChronometerValue = null;

    // Check if username is stored in GM storage
    GM.getValue('username').then(function(username) {
        if (!username) {
            alert('Username is not stored in GM storage.');
            username = prompt('Please enter your Papergames username (case-sensitive):');
            GM.setValue('username', username);
        }
    });

    // ===== REAL-TIME GUI DASHBOARD =====
    function createDashboard() {
        const dashboardHTML = `
            <div id="ttt-dashboard" style="
                position: fixed;
                top: 10px;
                right: 10px;
                width: 350px;
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                border: 2px solid #18bc9c;
                border-radius: 10px;
                color: white;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 12px;
                z-index: 10000;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                max-height: 90vh;
                overflow-y: auto;
            ">
                <div style="padding: 15px; border-bottom: 1px solid #18bc9c;">
                    <h3 style="margin: 0; color: #18bc9c; text-align: center;">🎯 Tic Tac Toe AI Dashboard</h3>
                    <div style="text-align: center; margin-top: 5px;">
                        <button id="ttt-start-btn" style="margin: 2px; padding: 5px 10px; background: #28a745; border: none; border-radius: 5px; color: white; cursor: pointer;">Start</button>
                        <button id="ttt-pause-btn" style="margin: 2px; padding: 5px 10px; background: #ffc107; border: none; border-radius: 5px; color: black; cursor: pointer;">Pause</button>
                        <button id="ttt-stop-btn" style="margin: 2px; padding: 5px 10px; background: #dc3545; border: none; border-radius: 5px; color: white; cursor: pointer;">Stop</button>
                        <button id="ttt-minimize-btn" style="margin: 2px; padding: 5px 10px; background: #6c757d; border: none; border-radius: 5px; color: white; cursor: pointer;">−</button>
                    </div>
                </div>

                <div id="ttt-dashboard-content" style="padding: 15px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                        <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-weight: bold; color: #18bc9c;">Session Time</div>
                            <div id="ttt-session-time">00:00:00</div>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-weight: bold; color: #18bc9c;">Win Rate</div>
                            <div id="ttt-win-rate">0%</div>
                        </div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Game Statistics</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 5px; text-align: center;">
                            <div>Wins: <span id="ttt-wins">0</span></div>
                            <div>Losses: <span id="ttt-losses">0</span></div>
                            <div>Draws: <span id="ttt-draws">0</span></div>
                        </div>
                        <div style="text-align: center; margin-top: 5px;">Total: <span id="ttt-total-games">0</span></div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Current Opponent</div>
                        <div>Name: <span id="ttt-opponent-name">None</span></div>
                        <div>Type: <span id="ttt-opponent-type">Unknown</span></div>
                        <div>Games: <span id="ttt-games-with-opponent">0</span></div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Opponent Stats</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px;">
                            <div>vs Humans: <span id="ttt-human-games">0</span></div>
                            <div>vs Bots: <span id="ttt-bot-games">0</span></div>
                        </div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Recent Games</div>
                        <div id="ttt-recent-games" style="max-height: 100px; overflow-y: auto; font-size: 11px;"></div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Settings</div>
                        <div style="margin-bottom: 10px;">
                            <label style="display: block; margin-bottom: 5px;">AI Depth: <span id="ttt-depth-value">100</span></label>
                            <input type="range" id="ttt-depth-slider" min="1" max="100" value="100" style="width: 100%; margin-bottom: 10px;">
                        </div>
                        <div style="margin-bottom: 10px;">
                            <label style="display: block; margin-bottom: 5px;">
                                <input type="checkbox" id="ttt-auto-queue" style="margin-right: 5px;">
                                Auto Queue (Legacy Mode)
                            </label>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <label style="display: block; margin-bottom: 5px;">Detection Confidence: <span id="ttt-confidence-value">0.7</span></label>
                            <input type="range" id="ttt-confidence-slider" min="0.1" max="1.0" step="0.1" value="0.7" style="width: 100%;">
                        </div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Game Cycle Status</div>
                        <div>Current Cycle: <span id="ttt-cycle-status">Waiting for opponent</span></div>
                        <div>Games in Cycle: <span id="ttt-cycle-progress">0/0</span></div>
                        <div>Next Action: <span id="ttt-next-action">Detect opponent</span></div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Error Log</div>
                        <div id="ttt-error-log" style="max-height: 80px; overflow-y: auto; font-size: 11px;"></div>
                    </div>

                    <div style="text-align: center;">
                        <button id="ttt-export-btn" style="padding: 8px 15px; background: #17a2b8; border: none; border-radius: 5px; color: white; cursor: pointer; margin: 2px;">Export Data</button>
                        <button id="ttt-reset-btn" style="padding: 8px 15px; background: #dc3545; border: none; border-radius: 5px; color: white; cursor: pointer; margin: 2px;">Reset Stats</button>
                        <button id="ttt-logout-btn" style="padding: 8px 15px; background: #6c757d; border: none; border-radius: 5px; color: white; cursor: pointer; margin: 2px;">Logout</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dashboardHTML);
        dashboard = document.getElementById('ttt-dashboard');

        // Add event listeners
        document.getElementById('ttt-start-btn').addEventListener('click', startBot);
        document.getElementById('ttt-pause-btn').addEventListener('click', pauseBot);
        document.getElementById('ttt-stop-btn').addEventListener('click', stopBot);
        document.getElementById('ttt-minimize-btn').addEventListener('click', toggleDashboard);
        document.getElementById('ttt-export-btn').addEventListener('click', exportData);
        document.getElementById('ttt-reset-btn').addEventListener('click', resetStats);
        document.getElementById('ttt-logout-btn').addEventListener('click', logout);

        // Settings event listeners
        document.getElementById('ttt-depth-slider').addEventListener('input', updateDepthSetting);
        document.getElementById('ttt-confidence-slider').addEventListener('input', updateConfidenceSetting);
        document.getElementById('ttt-auto-queue').addEventListener('change', toggleAutoQueue);

        updateDashboard();
    }

    function updateDashboard() {
        if (!dashboard) return;

        try {
            // Session time
            const sessionTime = Date.now() - gameState.sessionStartTime;
            const hours = Math.floor(sessionTime / 3600000);
            const minutes = Math.floor((sessionTime % 3600000) / 60000);
            const seconds = Math.floor((sessionTime % 60000) / 1000);
            document.getElementById('ttt-session-time').textContent =
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            // Win rate
            const winRate = gameState.totalGames > 0 ? (gameState.wins / gameState.totalGames * 100).toFixed(1) : 0;
            document.getElementById('ttt-win-rate').textContent = `${winRate}%`;

            // Game statistics
            document.getElementById('ttt-wins').textContent = gameState.wins;
            document.getElementById('ttt-losses').textContent = gameState.losses;
            document.getElementById('ttt-draws').textContent = gameState.draws;
            document.getElementById('ttt-total-games').textContent = gameState.totalGames;

            // Current opponent
            document.getElementById('ttt-opponent-name').textContent = gameState.currentOpponent || 'None';
            document.getElementById('ttt-opponent-type').textContent = gameState.opponentType || 'Unknown';
            document.getElementById('ttt-games-with-opponent').textContent = gameState.gamesWithCurrentOpponent;

            // Opponent stats
            const humanGames = gameState.gameHistory.filter(g => g.opponentType === 'human').length;
            const botGames = gameState.gameHistory.filter(g => g.opponentType === 'bot').length;
            document.getElementById('ttt-human-games').textContent = humanGames;
            document.getElementById('ttt-bot-games').textContent = botGames;

            // Recent games
            const recentGamesHtml = gameState.gameHistory.slice(-10).reverse().map(game => {
                const time = new Date(game.timestamp).toLocaleTimeString();
                const resultColor = game.result === 'win' ? '#28a745' : game.result === 'loss' ? '#dc3545' : '#ffc107';
                return `<div style="margin: 2px 0; color: ${resultColor};">${time} - ${game.result.toUpperCase()} vs ${game.opponent} (${game.opponentType})</div>`;
            }).join('');
            document.getElementById('ttt-recent-games').innerHTML = recentGamesHtml || '<div style="color: #6c757d;">No games yet</div>';

            // Game cycle status
            const maxGames = gameState.opponentType === 'human' ? CONFIG.HUMAN_GAMES_LIMIT :
                           gameState.opponentType === 'bot' ? CONFIG.BOT_GAMES_LIMIT : 0;

            let cycleStatus = 'Waiting for opponent';
            let nextAction = 'Detect opponent';

            if (gameState.currentOpponent && gameState.opponentType) {
                cycleStatus = `Playing vs ${gameState.opponentType}`;
                if (gameState.gamesWithCurrentOpponent < maxGames) {
                    nextAction = 'Continue playing';
                } else {
                    nextAction = 'Leave and find new opponent';
                }
            }

            document.getElementById('ttt-cycle-status').textContent = cycleStatus;
            document.getElementById('ttt-cycle-progress').textContent = `${gameState.gamesWithCurrentOpponent}/${maxGames}`;
            document.getElementById('ttt-next-action').textContent = nextAction;

            // Settings values
            document.getElementById('ttt-depth-value').textContent = depth;
            document.getElementById('ttt-depth-slider').value = depth;
            document.getElementById('ttt-confidence-value').textContent = CONFIG.DETECTION_CONFIDENCE_THRESHOLD.toFixed(1);
            document.getElementById('ttt-confidence-slider').value = CONFIG.DETECTION_CONFIDENCE_THRESHOLD;
            document.getElementById('ttt-auto-queue').checked = isAutoQueueOn;

            // Error log
            const errorLogHtml = gameState.errors.slice(-5).reverse().map(error => {
                const time = new Date(error.timestamp).toLocaleTimeString();
                return `<div style="margin: 2px 0; color: #dc3545;">${time} - ${error.message}</div>`;
            }).join('');
            document.getElementById('ttt-error-log').innerHTML = errorLogHtml || '<div style="color: #6c757d;">No errors</div>';

        } catch (error) {
            console.error('Error updating dashboard:', error);
        }
    }

    function startBot() {
        gameState.isRunning = true;
        gameState.isPaused = false;
        log('Bot started');

        // Start main intervals
        intervals.push(setInterval(updateDashboard, 1000));
        intervals.push(setInterval(saveGameState, CONFIG.SAVE_INTERVAL));
        intervals.push(setInterval(mainGameLoop, CONFIG.POLLING_INTERVAL));

        updateDashboard();
    }

    function pauseBot() {
        gameState.isPaused = !gameState.isPaused;
        log(gameState.isPaused ? 'Bot paused' : 'Bot resumed');
        updateDashboard();
    }

    function stopBot() {
        gameState.isRunning = false;
        gameState.isPaused = false;

        // Clear all intervals
        intervals.forEach(interval => clearInterval(interval));
        intervals = [];

        log('Bot stopped');
        updateDashboard();
    }

    function toggleDashboard() {
        const content = document.getElementById('ttt-dashboard-content');
        const btn = document.getElementById('ttt-minimize-btn');

        if (content.style.display === 'none') {
            content.style.display = 'block';
            btn.textContent = '−';
        } else {
            content.style.display = 'none';
            btn.textContent = '+';
        }
    }

    function exportData() {
        const data = {
            sessionStats: {
                startTime: gameState.sessionStartTime,
                totalGames: gameState.totalGames,
                wins: gameState.wins,
                losses: gameState.losses,
                draws: gameState.draws,
                winRate: gameState.totalGames > 0 ? (gameState.wins / gameState.totalGames * 100).toFixed(2) : 0
            },
            gameHistory: gameState.gameHistory,
            detectionLog: gameState.detectionLog,
            errors: gameState.errors
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ttt-session-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        a.click();
        URL.revokeObjectURL(url);

        log('Session data exported');
    }

    function resetStats() {
        if (confirm('Are you sure you want to reset all statistics? This cannot be undone.')) {
            gameState.totalGames = 0;
            gameState.wins = 0;
            gameState.losses = 0;
            gameState.draws = 0;
            gameState.gameHistory = [];
            gameState.detectionLog = [];
            gameState.errors = [];
            gameState.sessionStartTime = Date.now();

            saveGameState();
            updateDashboard();
            log('Statistics reset');
        }
    }

    // ===== SETTINGS CONTROL FUNCTIONS =====
    function updateDepthSetting() {
        const slider = document.getElementById('ttt-depth-slider');
        depth = parseInt(slider.value);
        GM.setValue('depth', depth.toString());
        document.getElementById('ttt-depth-value').textContent = depth;
        log(`AI depth updated to: ${depth}`);
    }

    function updateConfidenceSetting() {
        const slider = document.getElementById('ttt-confidence-slider');
        CONFIG.DETECTION_CONFIDENCE_THRESHOLD = parseFloat(slider.value);
        document.getElementById('ttt-confidence-value').textContent = CONFIG.DETECTION_CONFIDENCE_THRESHOLD.toFixed(1);
        log(`Detection confidence threshold updated to: ${CONFIG.DETECTION_CONFIDENCE_THRESHOLD}`);
    }

    function toggleAutoQueue() {
        const checkbox = document.getElementById('ttt-auto-queue');
        isAutoQueueOn = checkbox.checked;
        GM.setValue('isToggled', isAutoQueueOn);
        log(`Legacy auto-queue ${isAutoQueueOn ? 'enabled' : 'disabled'}`);
    }

    //------------------------------------------------
    // Legacy auto-queue functionality (simplified)
    var isAutoQueueOn = false;

    // Legacy auto-queue button checking
    function legacyAutoQueueCheck() {
        if (isAutoQueueOn && !gameState.isRunning) {
            // Check for leave button first using centralized selectors
            var leaveButton = document.querySelector(CONFIG.SELECTORS.LEAVE_BUTTON.PRIMARY) ||
                              document.querySelector(CONFIG.SELECTORS.LEAVE_BUTTON.FALLBACK);

            // Check for play online button using centralized selectors
            var playButton = document.querySelector(CONFIG.SELECTORS.PLAY_ONLINE_BUTTON.PRIMARY) ||
                             document.querySelector(CONFIG.SELECTORS.PLAY_ONLINE_BUTTON.FALLBACK);

            if (leaveButton) {
                leaveButton.click();
                log('Legacy auto-queue: Clicked leave button');
            } else if (playButton) {
                playButton.click();
                log('Legacy auto-queue: Clicked play online button');
            }
        }
    }

    // Set up legacy auto-queue checking
    setInterval(legacyAutoQueueCheck, 2000);

    //------------------------------------------------

    // ===== MAIN GAME LOOP =====
    async function mainGameLoop() {
        if (!gameState.isRunning || gameState.isPaused) {
            return;
        }

        try {
            // Check session duration
            if (Date.now() - gameState.sessionStartTime >= CONFIG.SESSION_DURATION) {
                log('24-hour session completed successfully');
                stopBot();
                return;
            }

            // Check loss limit (critical safety check)
            if (gameState.losses >= CONFIG.MAX_LOSSES_24H) {
                log(`CRITICAL: Maximum losses (${CONFIG.MAX_LOSSES_24H}) reached. Bot stopped for safety.`, 'error');
                stopBot();
                return;
            }

            // Detect new opponent if needed
            if (!gameState.currentOpponent) {
                await detectNewOpponent();
            }

            // Check for game state changes
            checkGameState();

            // Make AI move if it's our turn
            if (gameState.isGameActive && await isOurTurn()) {
                await makeAIMove();
            }

            // Update dashboard periodically
            updateDashboard();

        } catch (error) {
            log(`Error in main game loop: ${error.message}`, 'error');
        }
    }

    async function detectNewOpponent() {
        try {
            const opponentName = await getCurrentOpponentName();
            if (opponentName && opponentName !== gameState.currentOpponent) {
                gameState.currentOpponent = opponentName;
                gameState.gamesWithCurrentOpponent = 0;

                const detection = detectOpponentType(opponentName);
                gameState.opponentType = detection.type;

                log(`New opponent detected: ${opponentName} (${detection.type})`);
                updateDashboard();
            }
        } catch (error) {
            log(`Error detecting opponent: ${error.message}`, 'error');
        }
    }

    function checkGameState() {
        try {
            const board = getBoardState();
            if (!board) return;

            // Check if game just started
            const isEmpty = board.every(row => row.every(cell => cell === '_'));
            if (isEmpty && !gameState.isGameActive) {
                gameState.isGameActive = true;
                gameState.boardState = board;
                log('New game started');
                return;
            }

            // Check if game ended
            if (gameState.isGameActive) {
                const result = checkGameResult(board);
                if (result) {
                    gameState.isGameActive = false;
                    handleGameEnd(result);
                    return;
                }
            }

            gameState.boardState = board;
        } catch (error) {
            log(`Error checking game state: ${error.message}`, 'error');
        }
    }

    function checkGameResult(board) {
        // Check for win/loss/draw
        const winner = evaluateWinner(board);
        if (winner === gameState.currentPlayer) {
            return 'win';
        } else if (winner && winner !== gameState.currentPlayer) {
            return 'loss';
        } else if (!areMovesLeft(board)) {
            return 'draw';
        }
        return null;
    }

    async function isOurTurn() {
        try {
            const username = await GM.getValue("username");
            const profileOpeners = document.querySelectorAll(".text-truncate.cursor-pointer");
            let profileOpener = null;

            profileOpeners.forEach(function(opener) {
                if (opener.textContent.trim() === username) {
                    profileOpener = opener;
                }
            });

            if (!profileOpener) return false;

            const chronometer = document.querySelector("app-chronometer");
            const numberElement = profileOpener.parentNode ? profileOpener.parentNode.querySelectorAll("span")[4] : null;
            const currentElement = chronometer || numberElement;

            return currentElement && currentElement.textContent !== prevChronometerValue;
        } catch (error) {
            log(`Error checking turn: ${error.message}`, 'error');
            return false;
        }
    }

    async function makeAIMove() {
        try {
            const board = getBoardState();
            if (!board) return;

            // Determine our player symbol
            if (!gameState.currentPlayer) {
                gameState.currentPlayer = await determinePlayerSymbol();
            }

            const bestMove = findBestMove(board, gameState.currentPlayer);
            if (bestMove.row !== -1 && bestMove.col !== -1) {
                simulateCellClick(bestMove.row, bestMove.col);
                prevChronometerValue = document.querySelector("app-chronometer")?.textContent ||
                                     document.querySelector(".text-truncate.cursor-pointer")?.parentNode?.querySelectorAll("span")[4]?.textContent;
            }
        } catch (error) {
            log(`Error making AI move: ${error.message}`, 'error');
        }
    }

    async function determinePlayerSymbol() {
        try {
            const username = await GM.getValue("username");
            const profileOpeners = document.querySelectorAll(".text-truncate.cursor-pointer");
            let profileOpener = null;

            profileOpeners.forEach(function(opener) {
                if (opener.textContent.trim() === username) {
                    profileOpener = opener;
                }
            });

            if (!profileOpener) return 'x';

            const profileOpenerParent = profileOpener.parentNode ? profileOpener.parentNode.parentNode : null;
            const svgElement = profileOpenerParent?.querySelector("circle[class*='circle-dark-stroked']") ||
                             profileOpenerParent?.querySelector("svg[class*='fa-xmark']");

            if (svgElement && svgElement.closest("circle[class*='circle-dark-stroked']")) {
                return 'o';
            } else if (svgElement && svgElement.closest("svg[class*='fa-xmark']")) {
                return 'x';
            }

            return 'x'; // Default
        } catch (error) {
            log(`Error determining player symbol: ${error.message}`, 'error');
            return 'x';
        }
    }
    
    function logBoardState() {
        // Attempt to log various variables and elements for debugging
        try {
            // Log row and col based on a hardcoded squareId for debugging
            var squareId = "00"; // Change this as needed for different squares
            var row = parseInt(squareId[0]);
            var col = parseInt(squareId[1]);
    
            console.log("Row:", row, "Col:", col);
    
            // Log username from GM storage
            GM.getValue("username").then(function(username) {
                console.log("Username from GM storage:", username);
    
                // Log profile openers
                var profileOpeners = document.querySelectorAll(".text-truncate.cursor-pointer");
                console.log("Profile Openers:", profileOpeners);
    
                var profileOpener = null;
    
                profileOpeners.forEach(function(opener) {
                    if (opener.textContent.trim() === username) {
                        profileOpener = opener;
                    }
                });
    
                console.log("Profile Opener:", profileOpener);
    
                // Log chronometer element
                var chronometer = document.querySelector("app-chronometer");
                console.log("Chronometer:", chronometer);
    
                // Log number element
                var numberElement = profileOpener ? profileOpener.parentNode.querySelectorAll("span")[4] : null;
                console.log("Number Element:", numberElement);
    
                // Log profile opener parent
                var profileOpenerParent = profileOpener ? profileOpener.parentNode.parentNode : null;
                console.log("Profile Opener Parent:", profileOpenerParent);
    
                // Log SVG element
                var svgElement = profileOpenerParent ? profileOpenerParent.querySelector("circle[class*='circle-dark-stroked']") : null;
                if (!svgElement && profileOpenerParent) {
                    svgElement = profileOpenerParent.querySelector("svg[class*='fa-xmark']");
                }
                console.log("SVG Element:", svgElement);
    
                // Determine and log the player
                var player = null;
                if (svgElement && svgElement.closest("circle[class*='circle-dark-stroked']")) {
                    player = 'o'; // Player is playing as "O"
                } else if (svgElement && svgElement.closest("svg[class*='fa-xmark']")) {
                    player = 'x'; // Player is playing as "X"
                }
                console.log("Player:", player);
    
                // Log current element
                var currentElement = chronometer || numberElement;
                console.log("Current Element:", currentElement);
    
                console.log("Logging complete for this iteration.\n");
            });
        } catch (error) {
            console.error("Error in logBoardState:", error);
        }
    }
    
    // Call logBoardState every 5 seconds
    setInterval(logBoardState, 5000);
    

    // ===== ENHANCED AI ALGORITHM =====
    function findBestMove(board, player) {
        log(`Finding best move for player: ${player}`);

        // Check for immediate win
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                if (board[i][j] === '_') {
                    board[i][j] = player;
                    if (evaluateWinner(board) === player) {
                        board[i][j] = '_';
                        log(`Found winning move at [${i}, ${j}]`);
                        return { row: i, col: j };
                    }
                    board[i][j] = '_';
                }
            }
        }

        // Check for immediate block
        const opponent = getOpponent(player);
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                if (board[i][j] === '_') {
                    board[i][j] = opponent;
                    if (evaluateWinner(board) === opponent) {
                        board[i][j] = '_';
                        log(`Found blocking move at [${i}, ${j}]`);
                        return { row: i, col: j };
                    }
                    board[i][j] = '_';
                }
            }
        }

        // Use minimax for optimal play
        var bestVal = -1000;
        var bestMove = { row: -1, col: -1 };

        for (var i = 0; i < 3; i++) {
            for (var j = 0; j < 3; j++) {
                if (board[i][j] === '_') {
                    board[i][j] = player;
                    var moveVal = minimax(board, 0, false, depth, player);
                    board[i][j] = '_';

                    if (moveVal > bestVal) {
                        bestMove.row = i;
                        bestMove.col = j;
                        bestVal = moveVal;
                    }
                }
            }
        }

        log(`Best move value: ${bestVal} at [${bestMove.row}, ${bestMove.col}]`);
        return bestMove;
    }

    function evaluateWinner(board) {
        // Check rows
        for (let row = 0; row < 3; row++) {
            if (board[row][0] === board[row][1] && board[row][1] === board[row][2] && board[row][0] !== '_') {
                return board[row][0];
            }
        }

        // Check columns
        for (let col = 0; col < 3; col++) {
            if (board[0][col] === board[1][col] && board[1][col] === board[2][col] && board[0][col] !== '_') {
                return board[0][col];
            }
        }

        // Check diagonals
        if (board[0][0] === board[1][1] && board[1][1] === board[2][2] && board[0][0] !== '_') {
            return board[0][0];
        }

        if (board[0][2] === board[1][1] && board[1][1] === board[2][0] && board[0][2] !== '_') {
            return board[0][2];
        }

        return null;
    }

    function getOpponent(player) {
        return player === 'x' ? 'o' : 'x';
    }

    function minimax(board, depth, isMaximizingPlayer, maxDepth, currentPlayer) {
        const winner = evaluateWinner(board);

        if (winner === currentPlayer) {
            return 10 - depth;
        } else if (winner && winner !== currentPlayer) {
            return depth - 10;
        }

        if (depth === maxDepth || !areMovesLeft(board)) {
            return 0;
        }

        if (isMaximizingPlayer) {
            var best = -1000;

            for (var i = 0; i < 3; i++) {
                for (var j = 0; j < 3; j++) {
                    if (board[i][j] === '_') {
                        board[i][j] = currentPlayer;
                        best = Math.max(best, minimax(board, depth + 1, false, maxDepth, currentPlayer));
                        board[i][j] = '_';
                    }
                }
            }
            return best;
        } else {
            var best = 1000;

            for (var i = 0; i < 3; i++) {
                for (var j = 0; j < 3; j++) {
                    if (board[i][j] === '_') {
                        board[i][j] = getOpponent(currentPlayer);
                        best = Math.min(best, minimax(board, depth + 1, true, maxDepth, currentPlayer));
                        board[i][j] = '_';
                    }
                }
            }
            return best;
        }
    }

    function areMovesLeft(board) {
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                if (board[i][j] === '_') return true;
            }
        }
        return false;
    }

    // ===== INITIALIZATION =====
    async function initializeBot() {
        try {
            log('Initializing Tic Tac Toe AI Bot v4.0');

            // Load saved state
            await loadGameState();

            // Load depth setting
            const savedDepth = await GM.getValue('depth', 100);
            depth = parseInt(savedDepth);

            // Load auto-queue setting
            const savedAutoQueue = await GM.getValue('isToggled', false);
            isAutoQueueOn = savedAutoQueue;

            // Check username
            const username = await GM.getValue('username');
            if (!username) {
                alert('Username is not stored in GM storage.');
                const newUsername = prompt('Please enter your Papergames username (case-sensitive):');
                if (newUsername) {
                    await GM.setValue('username', newUsername);
                }
            }

            // Create dashboard
            createDashboard();

            // Auto-start if previously running
            if (gameState.isRunning) {
                startBot();
            }

            log('Bot initialization complete');

        } catch (error) {
            log(`Initialization error: ${error.message}`, 'error');
        }
    }

    // ===== LEGACY COMPATIBILITY =====
    function logout() {
        GM.deleteValue('username');
        location.reload();
    }

    // Start initialization when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeBot);
    } else {
        initializeBot();
    }
})();