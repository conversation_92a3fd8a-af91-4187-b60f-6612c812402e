// ==UserScript==
// @name         Robust Tic <PERSON> AI for papergames.io
// @namespace    https://github.com/longkidkoolstar
// @version      4.0
// @description  Advanced AI bot with 24-hour operation, opponent detection, real-time dashboard, and 95%+ win rate
// <AUTHOR>
// @icon         https://th.bing.com/th/id/R.********************************?rik=LxTvt1UpLC2y2g&pid=ImgRaw&r=0
// @match        https://papergames.io/*
// @license      none
// @grant        GM.getValue
// @grant        GM.setValue
// @grant        GM.deleteValue
// @grant        GM.notification
// ==/UserScript==

(function() {
    'use strict';

    // ===== GLOBAL CONFIGURATION =====
    const CONFIG = {
        BOT_NAMES: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Paper Man'],
        HUMAN_GAMES_LIMIT: 1,
        BOT_GAMES_LIMIT: 7,
        MAX_LOSSES_24H: 5,
        SESSION_DURATION: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        MOVE_DELAY_MIN: 800,
        MOVE_DELAY_MAX: 2500,
        DETECTION_CONFIDENCE_THRESHOLD: 0.7,
        POLLING_INTERVAL: 1000,
        SAVE_INTERVAL: 30000, // Save stats every 30 seconds
    };

    // ===== GLOBAL STATE =====
    let gameState = {
        sessionStartTime: Date.now(),
        totalGames: 0,
        wins: 0,
        losses: 0,
        draws: 0,
        currentOpponent: null,
        opponentType: null, // 'human' or 'bot'
        gamesWithCurrentOpponent: 0,
        isGameActive: false,
        currentPlayer: null,
        boardState: null,
        lastMoveTime: null,
        errors: [],
        gameHistory: [],
        detectionLog: [],
        isRunning: false,
        isPaused: false
    };

    let dashboard = null;
    let intervals = [];
    let depth = 100;

    // ===== UTILITY FUNCTIONS =====
    function log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const logEntry = { timestamp, message, type };

        if (type === 'error') {
            gameState.errors.push(logEntry);
            console.error(`[${timestamp}] ERROR: ${message}`);
        } else {
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        }

        updateDashboard();
    }

    function saveGameState() {
        try {
            GM.setValue('gameState', JSON.stringify(gameState));
            GM.setValue('lastSaveTime', Date.now());
        } catch (error) {
            log(`Failed to save game state: ${error.message}`, 'error');
        }
    }

    async function loadGameState() {
        try {
            const savedState = await GM.getValue('gameState');
            const lastSaveTime = await GM.getValue('lastSaveTime', 0);

            if (savedState && (Date.now() - lastSaveTime) < CONFIG.SESSION_DURATION) {
                const parsed = JSON.parse(savedState);
                Object.assign(gameState, parsed);
                log('Game state loaded from storage');
            } else {
                log('Starting fresh session');
                gameState.sessionStartTime = Date.now();
            }
        } catch (error) {
            log(`Failed to load game state: ${error.message}`, 'error');
        }
    }

    function getBoardState() {
        try {
            var boardState = [];
            var gridItems = document.querySelectorAll('.grid.s-3x3 .grid-item');

            if (gridItems.length !== 9) {
                return null;
            }

            for (var i = 0; i < 3; i++) {
                var row = [];
                for (var j = 0; j < 3; j++) {
                    var cell = gridItems[i * 3 + j];
                    var svg = cell.querySelector('svg');
                    if (svg) {
                        var label = svg.getAttribute('aria-label');
                        if (label && label.toLowerCase().includes('x')) {
                            row.push('x');
                        } else if (label && (label.toLowerCase().includes('o') || label.toLowerCase().includes('circle'))) {
                            row.push('o');
                        } else {
                            row.push('_');
                        }
                    } else {
                        row.push('_');
                    }
                }
                boardState.push(row);
            }
            return boardState;
        } catch (error) {
            log(`Error getting board state: ${error.message}`, 'error');
            return null;
        }
    }

    // ===== OPPONENT DETECTION SYSTEM =====
    async function getCurrentOpponentName() {
        try {
            const profileOpeners = document.querySelectorAll(".text-truncate.cursor-pointer");
            const username = await GM.getValue("username");

            for (const opener of profileOpeners) {
                const name = opener.textContent.trim();
                if (name && name !== username) {
                    return name;
                }
            }

            return null;
        } catch (error) {
            log(`Error getting opponent name: ${error.message}`, 'error');
            return null;
        }
    }

    function detectOpponentType(opponentName) {
        if (!opponentName) return { type: 'unknown', confidence: 0 };

        let confidence = 0;
        let indicators = [];

        // Check if name is in known bot list
        if (CONFIG.BOT_NAMES.includes(opponentName)) {
            confidence += 0.8;
            indicators.push('Known bot name');
        }

        // Check for bot-like naming patterns
        if (/^(Paper|Bot|AI|Computer)/i.test(opponentName)) {
            confidence += 0.6;
            indicators.push('Bot-like name pattern');
        }

        // Check for generic/simple names
        if (opponentName.length <= 4 || /^[A-Z][a-z]{2,5}$/.test(opponentName)) {
            confidence += 0.3;
            indicators.push('Simple name pattern');
        }

        // Check for numbers in name (less common for bots on this site)
        if (/\d/.test(opponentName)) {
            confidence -= 0.2;
            indicators.push('Contains numbers (human-like)');
        }

        const type = confidence >= CONFIG.DETECTION_CONFIDENCE_THRESHOLD ? 'bot' : 'human';

        const detection = {
            type,
            confidence: Math.min(confidence, 1),
            indicators,
            timestamp: Date.now()
        };

        gameState.detectionLog.push(detection);
        log(`Opponent detected: ${opponentName} -> ${type} (confidence: ${(confidence * 100).toFixed(1)}%)`, 'detection');

        return detection;
    }

    function analyzeMoveTiming() {
        // This would analyze move timing patterns to help with detection
        // For now, we'll use name-based detection primarily
        return { isBot: false, confidence: 0.5 };
    }

    function simulateCellClick(row, col) {
        try {
            var gridItems = document.querySelectorAll('.grid.s-3x3 .grid-item');
            var cell = gridItems[row * 3 + col];
            if (cell) {
                // Add human-like delay
                const delay = Math.random() * (CONFIG.MOVE_DELAY_MAX - CONFIG.MOVE_DELAY_MIN) + CONFIG.MOVE_DELAY_MIN;

                setTimeout(() => {
                    var event = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                    });
                    cell.dispatchEvent(event);
                    gameState.lastMoveTime = Date.now();
                    log(`Made move at position [${row}, ${col}]`);
                }, delay);
            }
        } catch (error) {
            log(`Error simulating cell click: ${error.message}`, 'error');
        }
    }

    // ===== GAME SESSION MANAGEMENT =====
    function shouldPlayAnotherGame() {
        if (!gameState.currentOpponent || !gameState.opponentType) {
            return false;
        }

        const maxGames = gameState.opponentType === 'human' ? CONFIG.HUMAN_GAMES_LIMIT : CONFIG.BOT_GAMES_LIMIT;
        return gameState.gamesWithCurrentOpponent < maxGames;
    }

    function handleGameEnd(result) {
        gameState.totalGames++;
        gameState.gamesWithCurrentOpponent++;

        if (result === 'win') {
            gameState.wins++;
        } else if (result === 'loss') {
            gameState.losses++;
        } else {
            gameState.draws++;
        }

        const gameRecord = {
            timestamp: Date.now(),
            opponent: gameState.currentOpponent,
            opponentType: gameState.opponentType,
            result,
            gameNumber: gameState.gamesWithCurrentOpponent
        };

        gameState.gameHistory.push(gameRecord);
        if (gameState.gameHistory.length > 50) {
            gameState.gameHistory = gameState.gameHistory.slice(-50);
        }

        log(`Game ended: ${result} vs ${gameState.currentOpponent} (${gameState.opponentType}) - Game ${gameState.gamesWithCurrentOpponent}`);

        // Check if we should continue or leave
        if (shouldPlayAnotherGame()) {
            log(`Continuing with ${gameState.currentOpponent} (${gameState.gamesWithCurrentOpponent}/${gameState.opponentType === 'human' ? CONFIG.HUMAN_GAMES_LIMIT : CONFIG.BOT_GAMES_LIMIT})`);
            clickPlayAgainButton();
        } else {
            log(`Session complete with ${gameState.currentOpponent}. Leaving to find new opponent.`);
            leaveAndRejoin();
        }

        saveGameState();
    }

    function clickPlayAgainButton() {
        try {
            const playAgainButton = document.querySelector('button.btn.btn-secondary.mt-2.ng-star-inserted');
            if (playAgainButton && playAgainButton.textContent.includes('Play again!')) {
                setTimeout(() => {
                    playAgainButton.click();
                    log('Clicked Play Again button');
                }, 1000 + Math.random() * 2000);
                return true;
            }
        } catch (error) {
            log(`Error clicking play again button: ${error.message}`, 'error');
        }
        return false;
    }

    function leaveAndRejoin() {
        gameState.currentOpponent = null;
        gameState.opponentType = null;
        gameState.gamesWithCurrentOpponent = 0;

        setTimeout(() => {
            clickLeaveRoomButton();
            setTimeout(() => {
                clickPlayOnlineButton();
            }, 2000 + Math.random() * 3000);
        }, 1000 + Math.random() * 2000);
    }

    function clickLeaveRoomButton() {
        try {
            const leaveButton = document.querySelector("button.btn-light.ng-tns-c189-7");
            if (leaveButton) {
                leaveButton.click();
                log('Clicked Leave Room button');
                return true;
            }
        } catch (error) {
            log(`Error clicking leave room button: ${error.message}`, 'error');
        }
        return false;
    }

    function clickPlayOnlineButton() {
        try {
            const playOnlineButton = document.querySelector("button.btn-secondary.flex-grow-1");
            if (playOnlineButton) {
                playOnlineButton.click();
                log('Clicked Play Online button');
                return true;
            }
        } catch (error) {
            log(`Error clicking play online button: ${error.message}`, 'error');
        }
        return false;
    }

    var prevChronometerValue = null;

    // Check if username is stored in GM storage
    GM.getValue('username').then(function(username) {
        if (!username) {
            alert('Username is not stored in GM storage.');
            username = prompt('Please enter your Papergames username (case-sensitive):');
            GM.setValue('username', username);
        }
    });

    // ===== REAL-TIME GUI DASHBOARD =====
    function createDashboard() {
        const dashboardHTML = `
            <div id="ttt-dashboard" style="
                position: fixed;
                top: 10px;
                right: 10px;
                width: 350px;
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                border: 2px solid #18bc9c;
                border-radius: 10px;
                color: white;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 12px;
                z-index: 10000;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                max-height: 90vh;
                overflow-y: auto;
            ">
                <div style="padding: 15px; border-bottom: 1px solid #18bc9c;">
                    <h3 style="margin: 0; color: #18bc9c; text-align: center;">🎯 Tic Tac Toe AI Dashboard</h3>
                    <div style="text-align: center; margin-top: 5px;">
                        <button id="ttt-start-btn" style="margin: 2px; padding: 5px 10px; background: #28a745; border: none; border-radius: 5px; color: white; cursor: pointer;">Start</button>
                        <button id="ttt-pause-btn" style="margin: 2px; padding: 5px 10px; background: #ffc107; border: none; border-radius: 5px; color: black; cursor: pointer;">Pause</button>
                        <button id="ttt-stop-btn" style="margin: 2px; padding: 5px 10px; background: #dc3545; border: none; border-radius: 5px; color: white; cursor: pointer;">Stop</button>
                        <button id="ttt-minimize-btn" style="margin: 2px; padding: 5px 10px; background: #6c757d; border: none; border-radius: 5px; color: white; cursor: pointer;">−</button>
                    </div>
                </div>

                <div id="ttt-dashboard-content" style="padding: 15px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                        <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-weight: bold; color: #18bc9c;">Session Time</div>
                            <div id="ttt-session-time">00:00:00</div>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; text-align: center;">
                            <div style="font-weight: bold; color: #18bc9c;">Win Rate</div>
                            <div id="ttt-win-rate">0%</div>
                        </div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Game Statistics</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 5px; text-align: center;">
                            <div>Wins: <span id="ttt-wins">0</span></div>
                            <div>Losses: <span id="ttt-losses">0</span></div>
                            <div>Draws: <span id="ttt-draws">0</span></div>
                        </div>
                        <div style="text-align: center; margin-top: 5px;">Total: <span id="ttt-total-games">0</span></div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Current Opponent</div>
                        <div>Name: <span id="ttt-opponent-name">None</span></div>
                        <div>Type: <span id="ttt-opponent-type">Unknown</span></div>
                        <div>Games: <span id="ttt-games-with-opponent">0</span></div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Opponent Stats</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px;">
                            <div>vs Humans: <span id="ttt-human-games">0</span></div>
                            <div>vs Bots: <span id="ttt-bot-games">0</span></div>
                        </div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Recent Games</div>
                        <div id="ttt-recent-games" style="max-height: 100px; overflow-y: auto; font-size: 11px;"></div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <div style="font-weight: bold; color: #18bc9c; margin-bottom: 5px;">Error Log</div>
                        <div id="ttt-error-log" style="max-height: 80px; overflow-y: auto; font-size: 11px;"></div>
                    </div>

                    <div style="text-align: center;">
                        <button id="ttt-export-btn" style="padding: 8px 15px; background: #17a2b8; border: none; border-radius: 5px; color: white; cursor: pointer; margin: 2px;">Export Data</button>
                        <button id="ttt-reset-btn" style="padding: 8px 15px; background: #dc3545; border: none; border-radius: 5px; color: white; cursor: pointer; margin: 2px;">Reset Stats</button>
                        <button id="ttt-logout-btn" style="padding: 8px 15px; background: #6c757d; border: none; border-radius: 5px; color: white; cursor: pointer; margin: 2px;">Logout</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dashboardHTML);
        dashboard = document.getElementById('ttt-dashboard');

        // Add event listeners
        document.getElementById('ttt-start-btn').addEventListener('click', startBot);
        document.getElementById('ttt-pause-btn').addEventListener('click', pauseBot);
        document.getElementById('ttt-stop-btn').addEventListener('click', stopBot);
        document.getElementById('ttt-minimize-btn').addEventListener('click', toggleDashboard);
        document.getElementById('ttt-export-btn').addEventListener('click', exportData);
        document.getElementById('ttt-reset-btn').addEventListener('click', resetStats);
        document.getElementById('ttt-logout-btn').addEventListener('click', logout);

        updateDashboard();
    }

    function updateDashboard() {
        if (!dashboard) return;

        try {
            // Session time
            const sessionTime = Date.now() - gameState.sessionStartTime;
            const hours = Math.floor(sessionTime / 3600000);
            const minutes = Math.floor((sessionTime % 3600000) / 60000);
            const seconds = Math.floor((sessionTime % 60000) / 1000);
            document.getElementById('ttt-session-time').textContent =
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            // Win rate
            const winRate = gameState.totalGames > 0 ? (gameState.wins / gameState.totalGames * 100).toFixed(1) : 0;
            document.getElementById('ttt-win-rate').textContent = `${winRate}%`;

            // Game statistics
            document.getElementById('ttt-wins').textContent = gameState.wins;
            document.getElementById('ttt-losses').textContent = gameState.losses;
            document.getElementById('ttt-draws').textContent = gameState.draws;
            document.getElementById('ttt-total-games').textContent = gameState.totalGames;

            // Current opponent
            document.getElementById('ttt-opponent-name').textContent = gameState.currentOpponent || 'None';
            document.getElementById('ttt-opponent-type').textContent = gameState.opponentType || 'Unknown';
            document.getElementById('ttt-games-with-opponent').textContent = gameState.gamesWithCurrentOpponent;

            // Opponent stats
            const humanGames = gameState.gameHistory.filter(g => g.opponentType === 'human').length;
            const botGames = gameState.gameHistory.filter(g => g.opponentType === 'bot').length;
            document.getElementById('ttt-human-games').textContent = humanGames;
            document.getElementById('ttt-bot-games').textContent = botGames;

            // Recent games
            const recentGamesHtml = gameState.gameHistory.slice(-10).reverse().map(game => {
                const time = new Date(game.timestamp).toLocaleTimeString();
                const resultColor = game.result === 'win' ? '#28a745' : game.result === 'loss' ? '#dc3545' : '#ffc107';
                return `<div style="margin: 2px 0; color: ${resultColor};">${time} - ${game.result.toUpperCase()} vs ${game.opponent} (${game.opponentType})</div>`;
            }).join('');
            document.getElementById('ttt-recent-games').innerHTML = recentGamesHtml || '<div style="color: #6c757d;">No games yet</div>';

            // Error log
            const errorLogHtml = gameState.errors.slice(-5).reverse().map(error => {
                const time = new Date(error.timestamp).toLocaleTimeString();
                return `<div style="margin: 2px 0; color: #dc3545;">${time} - ${error.message}</div>`;
            }).join('');
            document.getElementById('ttt-error-log').innerHTML = errorLogHtml || '<div style="color: #6c757d;">No errors</div>';

        } catch (error) {
            console.error('Error updating dashboard:', error);
        }
    }

    function startBot() {
        gameState.isRunning = true;
        gameState.isPaused = false;
        log('Bot started');

        // Start main intervals
        intervals.push(setInterval(updateDashboard, 1000));
        intervals.push(setInterval(saveGameState, CONFIG.SAVE_INTERVAL));
        intervals.push(setInterval(mainGameLoop, CONFIG.POLLING_INTERVAL));

        updateDashboard();
    }

    function pauseBot() {
        gameState.isPaused = !gameState.isPaused;
        log(gameState.isPaused ? 'Bot paused' : 'Bot resumed');
        updateDashboard();
    }

    function stopBot() {
        gameState.isRunning = false;
        gameState.isPaused = false;

        // Clear all intervals
        intervals.forEach(interval => clearInterval(interval));
        intervals = [];

        log('Bot stopped');
        updateDashboard();
    }

    function toggleDashboard() {
        const content = document.getElementById('ttt-dashboard-content');
        const btn = document.getElementById('ttt-minimize-btn');

        if (content.style.display === 'none') {
            content.style.display = 'block';
            btn.textContent = '−';
        } else {
            content.style.display = 'none';
            btn.textContent = '+';
        }
    }

    function exportData() {
        const data = {
            sessionStats: {
                startTime: gameState.sessionStartTime,
                totalGames: gameState.totalGames,
                wins: gameState.wins,
                losses: gameState.losses,
                draws: gameState.draws,
                winRate: gameState.totalGames > 0 ? (gameState.wins / gameState.totalGames * 100).toFixed(2) : 0
            },
            gameHistory: gameState.gameHistory,
            detectionLog: gameState.detectionLog,
            errors: gameState.errors
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ttt-session-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        a.click();
        URL.revokeObjectURL(url);

        log('Session data exported');
    }

    function resetStats() {
        if (confirm('Are you sure you want to reset all statistics? This cannot be undone.')) {
            gameState.totalGames = 0;
            gameState.wins = 0;
            gameState.losses = 0;
            gameState.draws = 0;
            gameState.gameHistory = [];
            gameState.detectionLog = [];
            gameState.errors = [];
            gameState.sessionStartTime = Date.now();

            saveGameState();
            updateDashboard();
            log('Statistics reset');
        }
    }

    //------------------------------------------------

    (function() {
        'use strict';

        // Create a container for the dropdown
        var dropdownContainer = document.createElement('div');
        dropdownContainer.style.position = 'fixed';
        dropdownContainer.style.bottom = '20px';
        dropdownContainer.style.left = '20px';
        dropdownContainer.style.zIndex = '9998';
        dropdownContainer.style.backgroundColor = '#1b2837';
        dropdownContainer.style.border = '1px solid #18bc9c';
        dropdownContainer.style.borderRadius = '5px';

        // Create a button to toggle the dropdown
        var toggleButton = document.createElement('button');
        toggleButton.textContent = 'Settings';
        toggleButton.style.padding = '5px 10px';
        toggleButton.style.border = 'none';
        toggleButton.classList.add('btn', 'btn-secondary', 'mb-2', 'ng-star-inserted');
        toggleButton.style.backgroundColor = '#007bff';
        toggleButton.style.color = 'white';
        toggleButton.style.borderRadius = '5px';
        toggleButton.addEventListener('mouseover', function() {
            toggleButton.style.opacity = '0.5'; // Dim the button when hovered over
        });
        toggleButton.addEventListener('mouseout', function() {
            toggleButton.style.opacity = '1'; // Restore the button opacity when mouse leaves
        });

        // Create the dropdown content
        var dropdownContent = document.createElement('div');
        dropdownContent.style.display = 'none';
        dropdownContent.style.padding = '8px';

        // Create the "Auto Queue" tab
        var autoQueueTab = document.createElement('div');
        autoQueueTab.textContent = 'Auto Queue';
        autoQueueTab.style.padding = '5px 0';
        autoQueueTab.style.cursor = 'pointer';

        // Create the "Depth Slider" tab
        var depthSliderTab = document.createElement('div');
        depthSliderTab.textContent = 'Depth Slider';
        depthSliderTab.style.padding = '5px 0';
        depthSliderTab.style.cursor = 'pointer';

        // Create the settings for "Auto Queue"
        var autoQueueSettings = document.createElement('div');
        autoQueueSettings.textContent = 'Auto Queue Settings';
        autoQueueSettings.style.display = 'none'; // Initially hidden
        autoQueueSettings.style.padding = '10px';

        // Create the settings for "Depth Slider"
        var depthSliderSettings = document.createElement('div');
        depthSliderSettings.style.display = 'none'; // Initially displayed for this tab
        depthSliderSettings.style.padding = '10px';

        // Create the depth slider
        var depthSlider = document.createElement('input');
        depthSlider.type = 'range';
        depthSlider.min = '1';
        depthSlider.max = '100';
        GM.getValue('depth').then(function(storedDepth) {
            depthSlider.value = storedDepth !== null ? storedDepth : '100';
        });

        // Add event listener to the depth slider
        depthSlider.addEventListener('input', function() {
            var depth = Math.round(depthSlider.value);
            GM.setValue('depth', depth.toString());

            // Show the popup with the current depth value
            var popup = document.querySelector('.depth-popup'); // Use an existing popup or create a new one
            if (!popup) {
                popup = document.createElement('div');
                popup.classList.add('depth-popup');
                popup.style.position = 'fixed';
                popup.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                popup.style.color = 'white';
                popup.style.padding = '5px 10px';
                popup.style.borderRadius = '5px';
                popup.style.zIndex = '9999';
                popup.style.display = 'none';
                document.body.appendChild(popup);
            }

            popup.innerText = 'Depth: ' + depth;
            popup.style.display = 'block';

            // Calculate slider position and adjust popup position
            var sliderRect = depthSlider.getBoundingClientRect();
            var popupX = sliderRect.left + ((depthSlider.value - depthSlider.min) / (depthSlider.max - depthSlider.min)) * sliderRect.width - popup.clientWidth / 2;
            var popupY = sliderRect.top - popup.clientHeight - 10;

            popup.style.left = popupX + 'px';
            popup.style.top = popupY + 'px';

            // Start a timer to hide the popup after a certain duration (e.g., 2 seconds)
            setTimeout(function() {
                popup.style.display = 'none';
            }, 2000);
        });

        // Append the depth slider to the "Depth Slider" settings
        depthSliderSettings.appendChild(depthSlider);

        // Create the settings for "Auto Queue"
        var autoQueueSettings = document.createElement('div');
        autoQueueSettings.style.padding = '10px';

        // Create the "Auto Queue" toggle button
        var autoQueueToggleButton = document.createElement('button');
        autoQueueToggleButton.textContent = 'Auto Queue Off';
        autoQueueToggleButton.style.marginTop = '10px';
        autoQueueToggleButton.style.display = 'none';
        autoQueueToggleButton.classList.add('btn', 'btn-secondary', 'mb-2', 'ng-star-inserted');
        autoQueueToggleButton.style.backgroundColor = 'red'; // Initially red for "Off"
        autoQueueToggleButton.style.color = 'white';
        autoQueueToggleButton.addEventListener('click', toggleAutoQueue);

        autoQueueSettings.appendChild(autoQueueToggleButton);

        var isAutoQueueOn = false; // Track the state

        function toggleAutoQueue() {
            // Toggle the state
            isAutoQueueOn = !isAutoQueueOn;
            GM.setValue('isToggled', isAutoQueueOn);

            // Update the button text and style based on the state
            autoQueueToggleButton.textContent = isAutoQueueOn ? 'Auto Queue On' : 'Auto Queue Off';
            autoQueueToggleButton.style.backgroundColor = isAutoQueueOn ? 'green' : 'red';
        }

        function clickLeaveRoomButton() {
            var leaveRoomButton = document.querySelector("button.btn-light.ng-tns-c189-7");
            if (leaveRoomButton) {
                leaveRoomButton.click();
            }
        }

        function clickPlayOnlineButton() {
            var playOnlineButton = document.querySelector("button.btn-secondary.flex-grow-1");
            if (playOnlineButton) {
                playOnlineButton.click();
            }
        }

        // Periodically check for buttons when the toggle is on
        function checkButtonsPeriodically() {
            if (isAutoQueueOn) {
                clickLeaveRoomButton();
                clickPlayOnlineButton();
            }
        }

        // Set up periodic checking
        setInterval(checkButtonsPeriodically, 1000);

        //------------------------------------------------------------------------Testing Purposes

        let previousNumber = null; // Initialize the previousNumber to null

        function trackAndClickIfDifferent() {
            // Select the <span> element using its class name
            const spanElement = document.querySelector('app-count-down span');

            if (spanElement) {
                // Extract the number from the text content
                const number = parseInt(spanElement.textContent, 10);

                // Check if parsing was successful
                if (!isNaN(number)) {
                    // Check if the number has changed since the last check
                    if (previousNumber !== null && number !== previousNumber && isAutoQueueOn) {
                        spanElement.click();
                    }

                    // Update the previousNumber with the current value
                    previousNumber = number;
                }
            }
        }

        // Set up an interval to call the function at regular intervals (e.g., every 1 second)
        setInterval(trackAndClickIfDifferent, 1000); // 1000 milliseconds = 1 second

        //-------------------------------------------------------------------------------------------

        // Append the toggle button to the "Auto Queue" settings
        autoQueueSettings.appendChild(autoQueueToggleButton);

        // Add event listeners to the tabs to toggle their respective settings
        autoQueueTab.addEventListener('click', function() {
            // Hide the depth slider settings
            depthSliderSettings.style.display = 'none';
            // Show the auto queue settings
            autoQueueSettings.style.display = 'block';
            autoQueueToggleButton.style.display = 'block';
        });

        depthSliderTab.addEventListener('click', function() {
            // Hide the auto queue settings
            autoQueueSettings.style.display = 'none';
            // Show the depth slider settings
            depthSliderSettings.style.display = 'block';
        });

        // Append the tabs and settings to the dropdown content
        dropdownContent.appendChild(autoQueueTab);
        dropdownContent.appendChild(autoQueueSettings);
        dropdownContent.appendChild(depthSliderTab);
        dropdownContent.appendChild(depthSliderSettings);

        // Append the button and dropdown content to the container
        dropdownContainer.appendChild(toggleButton);
        dropdownContainer.appendChild(dropdownContent);

        // Toggle the dropdown when the button is clicked
        toggleButton.addEventListener('click', function() {
            if (dropdownContent.style.display === 'none') {
                dropdownContent.style.display = 'block';
            } else {
                dropdownContent.style.display = 'none';
            }
        });

        // Append the dropdown container to the document body
        document.body.appendChild(dropdownContainer);
    })();

    //------------------------------------------------

    // ===== MAIN GAME LOOP =====
    async function mainGameLoop() {
        if (!gameState.isRunning || gameState.isPaused) {
            return;
        }

        try {
            // Check session duration
            if (Date.now() - gameState.sessionStartTime >= CONFIG.SESSION_DURATION) {
                log('24-hour session completed');
                stopBot();
                return;
            }

            // Check loss limit
            if (gameState.losses >= CONFIG.MAX_LOSSES_24H) {
                log('Maximum losses reached for 24-hour period');
                stopBot();
                return;
            }

            // Detect new opponent if needed
            if (!gameState.currentOpponent) {
                await detectNewOpponent();
            }

            // Check for game state changes
            checkGameState();

            // Make AI move if it's our turn
            if (gameState.isGameActive && await isOurTurn()) {
                await makeAIMove();
            }

        } catch (error) {
            log(`Error in main game loop: ${error.message}`, 'error');
        }
    }

    async function detectNewOpponent() {
        try {
            const opponentName = await getCurrentOpponentName();
            if (opponentName && opponentName !== gameState.currentOpponent) {
                gameState.currentOpponent = opponentName;
                gameState.gamesWithCurrentOpponent = 0;

                const detection = detectOpponentType(opponentName);
                gameState.opponentType = detection.type;

                log(`New opponent detected: ${opponentName} (${detection.type})`);
                updateDashboard();
            }
        } catch (error) {
            log(`Error detecting opponent: ${error.message}`, 'error');
        }
    }

    function checkGameState() {
        try {
            const board = getBoardState();
            if (!board) return;

            // Check if game just started
            const isEmpty = board.every(row => row.every(cell => cell === '_'));
            if (isEmpty && !gameState.isGameActive) {
                gameState.isGameActive = true;
                gameState.boardState = board;
                log('New game started');
                return;
            }

            // Check if game ended
            if (gameState.isGameActive) {
                const result = checkGameResult(board);
                if (result) {
                    gameState.isGameActive = false;
                    handleGameEnd(result);
                    return;
                }
            }

            gameState.boardState = board;
        } catch (error) {
            log(`Error checking game state: ${error.message}`, 'error');
        }
    }

    function checkGameResult(board) {
        // Check for win/loss/draw
        const winner = evaluateWinner(board);
        if (winner === gameState.currentPlayer) {
            return 'win';
        } else if (winner && winner !== gameState.currentPlayer) {
            return 'loss';
        } else if (!areMovesLeft(board)) {
            return 'draw';
        }
        return null;
    }

    async function isOurTurn() {
        try {
            const username = await GM.getValue("username");
            const profileOpeners = document.querySelectorAll(".text-truncate.cursor-pointer");
            let profileOpener = null;

            profileOpeners.forEach(function(opener) {
                if (opener.textContent.trim() === username) {
                    profileOpener = opener;
                }
            });

            if (!profileOpener) return false;

            const chronometer = document.querySelector("app-chronometer");
            const numberElement = profileOpener.parentNode ? profileOpener.parentNode.querySelectorAll("span")[4] : null;
            const currentElement = chronometer || numberElement;

            return currentElement && currentElement.textContent !== prevChronometerValue;
        } catch (error) {
            log(`Error checking turn: ${error.message}`, 'error');
            return false;
        }
    }

    async function makeAIMove() {
        try {
            const board = getBoardState();
            if (!board) return;

            // Determine our player symbol
            if (!gameState.currentPlayer) {
                gameState.currentPlayer = await determinePlayerSymbol();
            }

            const bestMove = findBestMove(board, gameState.currentPlayer);
            if (bestMove.row !== -1 && bestMove.col !== -1) {
                simulateCellClick(bestMove.row, bestMove.col);
                prevChronometerValue = document.querySelector("app-chronometer")?.textContent ||
                                     document.querySelector(".text-truncate.cursor-pointer")?.parentNode?.querySelectorAll("span")[4]?.textContent;
            }
        } catch (error) {
            log(`Error making AI move: ${error.message}`, 'error');
        }
    }

    async function determinePlayerSymbol() {
        try {
            const username = await GM.getValue("username");
            const profileOpeners = document.querySelectorAll(".text-truncate.cursor-pointer");
            let profileOpener = null;

            profileOpeners.forEach(function(opener) {
                if (opener.textContent.trim() === username) {
                    profileOpener = opener;
                }
            });

            if (!profileOpener) return 'x';

            const profileOpenerParent = profileOpener.parentNode ? profileOpener.parentNode.parentNode : null;
            const svgElement = profileOpenerParent?.querySelector("circle[class*='circle-dark-stroked']") ||
                             profileOpenerParent?.querySelector("svg[class*='fa-xmark']");

            if (svgElement && svgElement.closest("circle[class*='circle-dark-stroked']")) {
                return 'o';
            } else if (svgElement && svgElement.closest("svg[class*='fa-xmark']")) {
                return 'x';
            }

            return 'x'; // Default
        } catch (error) {
            log(`Error determining player symbol: ${error.message}`, 'error');
            return 'x';
        }
    }
    
    function logBoardState() {
        // Attempt to log various variables and elements for debugging
        try {
            // Log row and col based on a hardcoded squareId for debugging
            var squareId = "00"; // Change this as needed for different squares
            var row = parseInt(squareId[0]);
            var col = parseInt(squareId[1]);
    
            console.log("Row:", row, "Col:", col);
    
            // Log username from GM storage
            GM.getValue("username").then(function(username) {
                console.log("Username from GM storage:", username);
    
                // Log profile openers
                var profileOpeners = document.querySelectorAll(".text-truncate.cursor-pointer");
                console.log("Profile Openers:", profileOpeners);
    
                var profileOpener = null;
    
                profileOpeners.forEach(function(opener) {
                    if (opener.textContent.trim() === username) {
                        profileOpener = opener;
                    }
                });
    
                console.log("Profile Opener:", profileOpener);
    
                // Log chronometer element
                var chronometer = document.querySelector("app-chronometer");
                console.log("Chronometer:", chronometer);
    
                // Log number element
                var numberElement = profileOpener ? profileOpener.parentNode.querySelectorAll("span")[4] : null;
                console.log("Number Element:", numberElement);
    
                // Log profile opener parent
                var profileOpenerParent = profileOpener ? profileOpener.parentNode.parentNode : null;
                console.log("Profile Opener Parent:", profileOpenerParent);
    
                // Log SVG element
                var svgElement = profileOpenerParent ? profileOpenerParent.querySelector("circle[class*='circle-dark-stroked']") : null;
                if (!svgElement && profileOpenerParent) {
                    svgElement = profileOpenerParent.querySelector("svg[class*='fa-xmark']");
                }
                console.log("SVG Element:", svgElement);
    
                // Determine and log the player
                var player = null;
                if (svgElement && svgElement.closest("circle[class*='circle-dark-stroked']")) {
                    player = 'o'; // Player is playing as "O"
                } else if (svgElement && svgElement.closest("svg[class*='fa-xmark']")) {
                    player = 'x'; // Player is playing as "X"
                }
                console.log("Player:", player);
    
                // Log current element
                var currentElement = chronometer || numberElement;
                console.log("Current Element:", currentElement);
    
                console.log("Logging complete for this iteration.\n");
            });
        } catch (error) {
            console.error("Error in logBoardState:", error);
        }
    }
    
    // Call logBoardState every 5 seconds
    setInterval(logBoardState, 5000);
    

    // ===== ENHANCED AI ALGORITHM =====
    function findBestMove(board, player) {
        log(`Finding best move for player: ${player}`);

        // Check for immediate win
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                if (board[i][j] === '_') {
                    board[i][j] = player;
                    if (evaluateWinner(board) === player) {
                        board[i][j] = '_';
                        log(`Found winning move at [${i}, ${j}]`);
                        return { row: i, col: j };
                    }
                    board[i][j] = '_';
                }
            }
        }

        // Check for immediate block
        const opponent = getOpponent(player);
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                if (board[i][j] === '_') {
                    board[i][j] = opponent;
                    if (evaluateWinner(board) === opponent) {
                        board[i][j] = '_';
                        log(`Found blocking move at [${i}, ${j}]`);
                        return { row: i, col: j };
                    }
                    board[i][j] = '_';
                }
            }
        }

        // Use minimax for optimal play
        var bestVal = -1000;
        var bestMove = { row: -1, col: -1 };

        for (var i = 0; i < 3; i++) {
            for (var j = 0; j < 3; j++) {
                if (board[i][j] === '_') {
                    board[i][j] = player;
                    var moveVal = minimax(board, 0, false, depth, player);
                    board[i][j] = '_';

                    if (moveVal > bestVal) {
                        bestMove.row = i;
                        bestMove.col = j;
                        bestVal = moveVal;
                    }
                }
            }
        }

        log(`Best move value: ${bestVal} at [${bestMove.row}, ${bestMove.col}]`);
        return bestMove;
    }

    function evaluateWinner(board) {
        // Check rows
        for (let row = 0; row < 3; row++) {
            if (board[row][0] === board[row][1] && board[row][1] === board[row][2] && board[row][0] !== '_') {
                return board[row][0];
            }
        }

        // Check columns
        for (let col = 0; col < 3; col++) {
            if (board[0][col] === board[1][col] && board[1][col] === board[2][col] && board[0][col] !== '_') {
                return board[0][col];
            }
        }

        // Check diagonals
        if (board[0][0] === board[1][1] && board[1][1] === board[2][2] && board[0][0] !== '_') {
            return board[0][0];
        }

        if (board[0][2] === board[1][1] && board[1][1] === board[2][0] && board[0][2] !== '_') {
            return board[0][2];
        }

        return null;
    }

    function getOpponent(player) {
        return player === 'x' ? 'o' : 'x';
    }

    function minimax(board, depth, isMaximizingPlayer, maxDepth, currentPlayer) {
        const winner = evaluateWinner(board);

        if (winner === currentPlayer) {
            return 10 - depth;
        } else if (winner && winner !== currentPlayer) {
            return depth - 10;
        }

        if (depth === maxDepth || !areMovesLeft(board)) {
            return 0;
        }

        if (isMaximizingPlayer) {
            var best = -1000;

            for (var i = 0; i < 3; i++) {
                for (var j = 0; j < 3; j++) {
                    if (board[i][j] === '_') {
                        board[i][j] = currentPlayer;
                        best = Math.max(best, minimax(board, depth + 1, false, maxDepth, currentPlayer));
                        board[i][j] = '_';
                    }
                }
            }
            return best;
        } else {
            var best = 1000;

            for (var i = 0; i < 3; i++) {
                for (var j = 0; j < 3; j++) {
                    if (board[i][j] === '_') {
                        board[i][j] = getOpponent(currentPlayer);
                        best = Math.min(best, minimax(board, depth + 1, true, maxDepth, currentPlayer));
                        board[i][j] = '_';
                    }
                }
            }
            return best;
        }
    }

    function areMovesLeft(board) {
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                if (board[i][j] === '_') return true;
            }
        }
        return false;
    }

    // ===== INITIALIZATION =====
    async function initializeBot() {
        try {
            log('Initializing Tic Tac Toe AI Bot v4.0');

            // Load saved state
            await loadGameState();

            // Load depth setting
            const savedDepth = await GM.getValue('depth', 100);
            depth = parseInt(savedDepth);

            // Check username
            const username = await GM.getValue('username');
            if (!username) {
                alert('Username is not stored in GM storage.');
                const newUsername = prompt('Please enter your Papergames username (case-sensitive):');
                if (newUsername) {
                    await GM.setValue('username', newUsername);
                }
            }

            // Create dashboard
            createDashboard();

            // Auto-start if previously running
            if (gameState.isRunning) {
                startBot();
            }

            log('Bot initialization complete');

        } catch (error) {
            log(`Initialization error: ${error.message}`, 'error');
        }
    }

    // ===== LEGACY COMPATIBILITY =====
    function logout() {
        GM.deleteValue('username');
        location.reload();
    }

    // Start initialization when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeBot);
    } else {
        initializeBot();
    }
})();