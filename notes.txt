https://papergames.io/en/match-history/62e8d50791ca76002643de1a

https://papergames.io/en/match-history/{user_id}


button to copu the user id: document.querySelector("#mat-menu-panel-18 > div > button:nth-child(4)")


usernames of current game stored here: document.querySelector("body > app-root > app-navigation > div > div.d-flex.flex-column.h-100.w-100 > main > app-room > div > div > div.col-md-9.col-lg-8.bg-gray-000.h-100.position-relative.overflow-hidden.ng-tns-c1645232060-15 > div > app-room-players")

Enhance the existing TicTacToe bot with the following logic and behavior:

After each game, the bot should automatically click the "Play again!" button if it appears. Here’s the relevant HTML:

html
Copy code
<button class="btn btn-secondary mt-2 ng-star-inserted">
  <span class="ng-star-inserted">Play again!</span>
  <span class="short-cut-key ms-2">↵ Enter</span>
</button>
The bot should be able to run for 24 hours continuously with no critical bugs.

It must lose no more than 5 games total throughout the entire 24-hour session.

Implement logic to detect if the current opponent is a real human or a bot.

If it detects a real human, the bot should play only one game, then follow the regular routine of:

Clicking the "Leave game" button when the game ends.

Clicking the "Play Online" button to start searching for a new match.

If it detects a bot opponent, it should play 7 games in a row before repeating the same leave-and-rejoin routine.

This cycle (detect → play 1 or 7 games → leave → play online → repeat) should continue non-stop for the full 24-hour session.

