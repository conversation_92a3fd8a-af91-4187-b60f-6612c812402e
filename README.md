# Robust Tic Tac <PERSON>e AI for papergames.io

## Overview

This is an advanced AI bot designed for 24-hour continuous operation on papergames.io with a target win rate of 95%+. The bot features intelligent opponent detection, real-time monitoring dashboard, comprehensive error handling, and strategic game session management.

## Key Features

### 🎯 Core Performance
- **24-Hour Continuous Operation**: Runs reliably for 24 hours without critical failures
- **95%+ Win Rate**: Advanced minimax algorithm with immediate win/block detection
- **Maximum 5 Losses**: Automatically stops if loss limit is reached in 24 hours
- **Robust Error Handling**: Recovers from network issues, page reloads, and UI changes

### 🤖 Intelligent Opponent Detection
- **Bot vs Human Classification**: Uses multiple heuristics for accurate detection
- **Known Bot Database**: Maintains list of known bot usernames
- **Pattern Analysis**: Analyzes naming patterns and behaviors
- **Confidence Scoring**: Provides detection confidence levels

### 🎮 Strategic Game Management
- **Human Opponents**: Play exactly 1 game, then leave and rejoin
- **Bot Opponents**: Play exactly 7 consecutive games, then leave and rejoin
- **Automatic Navigation**: Handles "Leave game" and "Play Online" buttons
- **Play Again Detection**: Automatically clicks "Play again!" when available

### 📊 Real-Time Dashboard
- **Session Statistics**: Uptime, win rate, total games played
- **Opponent Analytics**: Games vs humans/bots, current opponent info
- **Game History**: Recent game results with timestamps
- **Error Monitoring**: Real-time error log and recovery actions
- **Control Panel**: Start/Stop/Pause controls with export functionality

### 🛡️ Advanced Error Recovery
- **DOM Element Detection**: Robust selectors with fallbacks
- **Network Issue Handling**: Automatic retry mechanisms
- **Page Reload Recovery**: Maintains state across refreshes
- **Graceful Degradation**: Continues operation despite minor errors

## Installation

1. Install [Tampermonkey](https://www.tampermonkey.net/) or [Greasemonkey](https://www.greasespot.net/)
2. Copy the contents of `Tic Tac Toe AI for papergames.user.js`
3. Create a new userscript in your extension
4. Paste the code and save
5. Navigate to [papergames.io](https://papergames.io)
6. Enter your username when prompted

## Usage

### Initial Setup
1. The bot will prompt for your papergames.io username (case-sensitive)
2. The dashboard will appear in the top-right corner
3. Click "Start" to begin automated play

### Dashboard Controls
- **Start**: Begin bot operation
- **Pause**: Temporarily pause without stopping
- **Stop**: Stop bot and clear intervals
- **Minimize**: Hide/show dashboard content
- **Export Data**: Download session statistics as JSON
- **Reset Stats**: Clear all statistics (requires confirmation)
- **Logout**: Clear username and reload page

### Monitoring
The dashboard provides real-time information about:
- Session duration and remaining time
- Current win rate percentage
- Total wins, losses, and draws
- Current opponent name and detected type
- Games played against humans vs bots
- Recent game history (last 10 games)
- Error log with timestamps

## Configuration

### Bot Detection Settings
```javascript
const CONFIG = {
    BOT_NAMES: ['Katha', 'Staci', 'Claudetta', 'Charline', 'Carolyne', 'Valerye', 'Rowena', 'Arabel', 'Zea', 'Paper Man'],
    HUMAN_GAMES_LIMIT: 1,
    BOT_GAMES_LIMIT: 7,
    MAX_LOSSES_24H: 5,
    DETECTION_CONFIDENCE_THRESHOLD: 0.7
};
```

### Timing Settings
```javascript
const CONFIG = {
    MOVE_DELAY_MIN: 800,        // Minimum delay between moves (ms)
    MOVE_DELAY_MAX: 2500,       // Maximum delay between moves (ms)
    POLLING_INTERVAL: 1000,     // Main loop interval (ms)
    SAVE_INTERVAL: 30000        // State save interval (ms)
};
```

## AI Strategy

### Minimax Algorithm
- **Immediate Win Detection**: Prioritizes winning moves
- **Immediate Block Detection**: Blocks opponent winning moves
- **Optimal Play**: Uses minimax for remaining decisions
- **Depth Control**: Configurable search depth (default: 100)

### Move Timing
- **Human-like Delays**: Random delays between 800-2500ms
- **Pattern Avoidance**: Varies timing to appear natural
- **Turn Detection**: Waits for proper turn indicators

## Opponent Detection Logic

### Bot Indicators (Increase Confidence)
- Name appears in known bot list (+0.8)
- Name matches bot patterns like "Paper", "Bot", "AI" (+0.6)
- Simple name patterns (4 chars or less) (+0.3)

### Human Indicators (Decrease Confidence)
- Name contains numbers (-0.2)
- Complex naming patterns
- Irregular timing patterns

### Detection Threshold
- Confidence ≥ 0.7 = Bot
- Confidence < 0.7 = Human

## Data Export

The export function provides comprehensive session data:

```json
{
  "sessionStats": {
    "startTime": 1699123456789,
    "totalGames": 150,
    "wins": 145,
    "losses": 3,
    "draws": 2,
    "winRate": "96.67"
  },
  "gameHistory": [...],
  "detectionLog": [...],
  "errors": [...]
}
```

## Troubleshooting

### Common Issues

**Bot not making moves:**
- Check if username is correctly set
- Verify the game board is visible
- Check console for error messages

**Dashboard not appearing:**
- Refresh the page
- Check if userscript is enabled
- Verify you're on papergames.io

**Opponent detection issues:**
- Update the BOT_NAMES list
- Adjust DETECTION_CONFIDENCE_THRESHOLD
- Check detection log in dashboard

### Error Recovery
The bot automatically handles:
- Network timeouts
- DOM element changes
- Page navigation issues
- Unexpected UI states

## Development

### Adding New Bot Names
Update the `BOT_NAMES` array in the configuration:
```javascript
BOT_NAMES: ['Katha', 'Staci', 'NewBotName', ...]
```

### Modifying Detection Logic
Adjust the `detectOpponentType` function to add new heuristics:
```javascript
// Add custom detection logic
if (customCondition) {
    confidence += 0.5;
    indicators.push('Custom indicator');
}
```

### Debugging
Enable detailed logging by checking the browser console and dashboard error log.

## Version History

### v4.0 (Current)
- Complete rewrite with modern architecture
- Real-time dashboard with comprehensive monitoring
- Advanced opponent detection system
- 24-hour continuous operation capability
- Robust error handling and recovery
- Strategic game session management
- Data export and analytics

### v3.0 (Previous)
- Basic AI with minimax algorithm
- Simple auto-queue functionality
- Manual depth control

## License

This project is provided as-is for educational purposes. Use responsibly and in accordance with papergames.io terms of service.

## Contributing

Feel free to submit issues and enhancement requests. When contributing:
1. Test thoroughly on papergames.io
2. Maintain backward compatibility
3. Update documentation
4. Follow existing code style

## Disclaimer

This bot is for educational and entertainment purposes. Users are responsible for compliance with papergames.io terms of service and fair play policies.
